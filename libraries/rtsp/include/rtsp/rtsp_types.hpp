#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <atomic>
#include <functional>

namespace aibox {
namespace rtsp {

// Forward declarations
class RTSPConnection;
class PacketReceiver;
class NALParser;
class StreamMultiplexer;

// Type aliases for RK3588 platform
using StreamId = std::string;
using Timestamp = std::chrono::steady_clock::time_point;
using Duration = std::chrono::milliseconds;

// Platform-specific constants for RK3588
namespace platform {
    // Orange Pi 5 Plus/Ultra memory limits
    constexpr size_t MEMORY_LIMIT_4GB_MB = 1200;
    constexpr size_t MEMORY_LIMIT_8GB_MB = 2500;
    constexpr size_t MEMORY_LIMIT_16GB_MB = 6000;

    // CPU core allocation (RK3588 big.LITTLE)
    constexpr int RTSP_CPU_CORES[] = {2, 3};  // Cortex-A55 cores

    // Thermal management
    constexpr int MAX_SOC_TEMPERATURE = 85;   // Celsius
    constexpr int THROTTLE_TEMPERATURE = 80; // Start throttling

    // Hardware accelerator limits
    constexpr int MAX_MPP_DECODER_INSTANCES = 16;  // Increased for 16GB
    constexpr int MAX_RGA_OPERATIONS_PER_SEC = 120; // Increased for 16GB
}

// RTSP connection states
enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    STREAMING,
    ERROR,
    RECONNECTING
};

// Transport protocols
enum class TransportProtocol {
    UDP,
    TCP,
    AUTO  // Let GStreamer decide
};

// Video codec types
enum class VideoCodec {
    H264,
    H265,
    UNKNOWN
};

// NAL unit types (H.264/H.265)
enum class NALUnitType {
    // H.264 NAL unit types
    H264_NON_IDR = 1,
    H264_IDR = 5,
    H264_SPS = 7,
    H264_PPS = 8,
    
    // H.265 NAL unit types  
    H265_VPS = 32,
    H265_SPS = 33,
    H265_PPS = 34,
    H265_IDR_W_RADL = 19,
    H265_IDR_N_LP = 20,
    
    UNKNOWN = 0
};

// Error categories for RK3588 platform
enum class ErrorCategory {
    NETWORK_ERROR,          // Connection failures, timeouts
    PROTOCOL_ERROR,         // RTSP/RTP protocol violations
    CODEC_ERROR,            // H.264/H.265 parsing errors
    HARDWARE_ERROR,         // MPP/RGA hardware failures
    RESOURCE_ERROR,         // Memory, thermal constraints
    CONFIGURATION_ERROR,    // Invalid configuration
    THERMAL_ERROR          // SoC overheating
};

// Stream priority levels
enum class StreamPriority {
    LOW = 0,
    MEDIUM = 1,
    HIGH = 2,
    CRITICAL = 3
};

// Hardware acceleration status
enum class HardwareAccelStatus {
    DISABLED,
    SOFTWARE_FALLBACK,
    HARDWARE_ACTIVE,
    HARDWARE_ERROR
};

// Basic data structures
struct Resolution {
    uint32_t width;
    uint32_t height;
    
    Resolution() : width(0), height(0) {}
    Resolution(uint32_t w, uint32_t h) : width(w), height(h) {}
    
    bool isValid() const { return width > 0 && height > 0; }
    uint64_t pixelCount() const { return static_cast<uint64_t>(width) * height; }
};

struct FrameRate {
    uint32_t numerator;
    uint32_t denominator;
    
    FrameRate() : numerator(30), denominator(1) {}
    FrameRate(uint32_t num, uint32_t den = 1) : numerator(num), denominator(den) {}
    
    double toDouble() const { 
        return denominator > 0 ? static_cast<double>(numerator) / denominator : 0.0; 
    }
};

// RTP packet header structure (RFC 3550)
struct RTPHeader {
    uint8_t version;            // Version (should be 2)
    uint8_t padding;            // Padding flag
    uint8_t extension;          // Extension flag
    uint8_t csrc_count;         // CSRC count
    uint8_t marker;             // Marker bit
    uint8_t payload_type;       // Payload type
    uint16_t sequence_number;   // Sequence number
    uint32_t timestamp;         // Timestamp
    uint32_t ssrc;              // SSRC identifier

    RTPHeader() : version(2), padding(0), extension(0), csrc_count(0),
                  marker(0), payload_type(0), sequence_number(0),
                  timestamp(0), ssrc(0) {}

    bool isValid() const { return version == 2; }
    size_t getHeaderSize() const { return 12 + static_cast<size_t>(csrc_count * 4); }
};

// RTP packet structure
struct RTPPacket {
    RTPHeader header;
    std::vector<uint32_t> csrc_list;    // Contributing source list
    std::vector<uint8_t> extension_data; // Extension data if present
    std::vector<uint8_t> payload;       // Actual payload data
    Timestamp receive_time;             // When packet was received

    RTPPacket() = default;

    size_t getTotalSize() const {
        return sizeof(RTPHeader) + (csrc_list.size() * 4) +
               extension_data.size() + payload.size();
    }

    bool hasExtension() const { return header.extension; }
    bool hasPadding() const { return header.padding; }
    bool isMarker() const { return header.marker; }
};

// RTCP packet types (RFC 3550)
enum class RTCPPacketType {
    SENDER_REPORT = 200,
    RECEIVER_REPORT = 201,
    SOURCE_DESCRIPTION = 202,
    BYE = 203,
    APP = 204,
    UNKNOWN = 0
};

// RTCP packet structure
struct RTCPPacket {
    RTCPPacketType type;
    std::vector<uint8_t> data;
    Timestamp receive_time;

    RTCPPacket() : type(RTCPPacketType::UNKNOWN) {}

    size_t size() const { return data.size(); }
    bool empty() const { return data.empty(); }
};

// NAL unit data structure
struct NALUnit {
    NALUnitType type;
    std::vector<uint8_t> data;
    Timestamp timestamp;
    uint32_t sequence_number;
    StreamId stream_id;
    bool is_keyframe;

    NALUnit() : type(NALUnitType::UNKNOWN), sequence_number(0), is_keyframe(false) {}

    size_t size() const { return data.size(); }
    bool empty() const { return data.empty(); }
    void clear() { data.clear(); }
};

// Non-atomic packet statistics for return values
struct PacketStatistics {
    // RTP packet stats
    uint64_t packets_received = 0;
    uint64_t packets_lost = 0;
    uint64_t bytes_received = 0;
    uint64_t duplicate_packets = 0;
    uint64_t out_of_order_packets = 0;

    // RTCP stats
    uint64_t rtcp_packets_received = 0;
    uint64_t sender_reports_received = 0;
    uint64_t receiver_reports_sent = 0;

    // Jitter buffer stats
    float average_jitter_ms = 0.0f;
    uint32_t current_queue_depth = 0;
    uint32_t max_queue_depth = 0;
    uint64_t buffer_overruns = 0;
    uint64_t buffer_underruns = 0;

    // Hardware acceleration stats
    uint64_t hardware_accelerated_count = 0;
    uint64_t software_fallback_count = 0;
    uint64_t rga_processing_count = 0;
    uint64_t dmabuf_operations = 0;

    // Performance metrics
    uint32_t current_fps = 0;
    uint32_t current_bitrate_kbps = 0;
    uint32_t average_latency_ms = 0;

    // Error counters
    uint32_t parsing_errors = 0;
    uint32_t hardware_errors = 0;
    uint32_t network_errors = 0;

    // Timestamps
    Timestamp last_packet_time;
    Timestamp first_packet_time;

    double getPacketLossRate() const {
        uint64_t total = packets_received + packets_lost;
        return total > 0 ? static_cast<double>(packets_lost) / static_cast<double>(total) : 0.0;
    }
};

// Atomic packet statistics for detailed monitoring
struct AtomicPacketStatistics {
    // RTP packet stats
    std::atomic<uint64_t> packets_received{0};
    std::atomic<uint64_t> packets_lost{0};
    std::atomic<uint64_t> bytes_received{0};
    std::atomic<uint64_t> duplicate_packets{0};
    std::atomic<uint64_t> out_of_order_packets{0};

    // RTCP stats
    std::atomic<uint64_t> rtcp_packets_received{0};
    std::atomic<uint64_t> sender_reports_received{0};
    std::atomic<uint64_t> receiver_reports_sent{0};

    // Jitter buffer stats
    std::atomic<float> average_jitter_ms{0.0f};
    std::atomic<uint32_t> current_queue_depth{0};
    std::atomic<uint32_t> max_queue_depth{0};
    std::atomic<uint64_t> buffer_overruns{0};
    std::atomic<uint64_t> buffer_underruns{0};

    // Hardware acceleration stats
    std::atomic<uint64_t> hardware_accelerated_count{0};
    std::atomic<uint64_t> software_fallback_count{0};
    std::atomic<uint64_t> rga_processing_count{0};
    std::atomic<uint64_t> dmabuf_operations{0};

    // Performance metrics
    std::atomic<uint32_t> current_fps{0};
    std::atomic<uint32_t> current_bitrate_kbps{0};
    std::atomic<uint32_t> average_latency_ms{0};

    // Error counters
    std::atomic<uint32_t> parsing_errors{0};
    std::atomic<uint32_t> hardware_errors{0};
    std::atomic<uint32_t> network_errors{0};

    // Timestamps
    Timestamp last_packet_time;
    Timestamp first_packet_time;

    void reset() {
        packets_received = 0;
        packets_lost = 0;
        bytes_received = 0;
        duplicate_packets = 0;
        out_of_order_packets = 0;
        rtcp_packets_received = 0;
        sender_reports_received = 0;
        receiver_reports_sent = 0;
        average_jitter_ms = 0.0f;
        current_queue_depth = 0;
        max_queue_depth = 0;
        buffer_overruns = 0;
        buffer_underruns = 0;
        hardware_accelerated_count = 0;
        software_fallback_count = 0;
        rga_processing_count = 0;
        dmabuf_operations = 0;
        current_fps = 0;
        current_bitrate_kbps = 0;
        average_latency_ms = 0;
        parsing_errors = 0;
        hardware_errors = 0;
        network_errors = 0;
        last_packet_time = std::chrono::steady_clock::now();
        first_packet_time = std::chrono::steady_clock::now();
    }
};

// Stream statistics for monitoring (non-atomic for return values)
struct StreamStatistics {
    // Connection stats
    uint64_t packets_received = 0;
    uint64_t packets_lost = 0;
    uint64_t bytes_received = 0;
    uint32_t reconnect_count = 0;

    // Performance stats
    uint32_t current_fps = 0;
    uint32_t current_bitrate_kbps = 0;
    uint32_t average_latency_ms = 0;

    // Hardware acceleration stats
    uint32_t mpp_decode_count = 0;
    uint32_t rga_process_count = 0;
    uint32_t software_fallback_count = 0;

    // Resource usage
    size_t memory_usage_bytes = 0;
    uint32_t cpu_usage_percent = 0;

    // Error stats
    uint32_t network_errors = 0;
    uint32_t decode_errors = 0;
    uint32_t hardware_errors = 0;

    // Timestamps
    Timestamp last_packet_time;
    Timestamp connection_start_time;

    double getPacketLossRate() const {
        uint64_t total = packets_received + packets_lost;
        return total > 0 ? static_cast<double>(packets_lost) / total : 0.0;
    }

    double getHardwareAccelRate() const {
        uint64_t total = mpp_decode_count + software_fallback_count;
        return total > 0 ? static_cast<double>(mpp_decode_count) / total : 0.0;
    }
};

// Internal atomic statistics for thread-safe updates
struct AtomicStreamStatistics {
    // Connection stats
    std::atomic<uint64_t> packets_received{0};
    std::atomic<uint64_t> packets_lost{0};
    std::atomic<uint64_t> bytes_received{0};
    std::atomic<uint32_t> reconnect_count{0};

    // Performance stats
    std::atomic<uint32_t> current_fps{0};
    std::atomic<uint32_t> current_bitrate_kbps{0};
    std::atomic<uint32_t> average_latency_ms{0};

    // Hardware acceleration stats
    std::atomic<uint32_t> mpp_decode_count{0};
    std::atomic<uint32_t> rga_process_count{0};
    std::atomic<uint32_t> software_fallback_count{0};

    // Resource usage
    std::atomic<size_t> memory_usage_bytes{0};
    std::atomic<uint32_t> cpu_usage_percent{0};

    // Error stats
    std::atomic<uint32_t> network_errors{0};
    std::atomic<uint32_t> decode_errors{0};
    std::atomic<uint32_t> hardware_errors{0};

    // Timestamps
    Timestamp last_packet_time;
    Timestamp connection_start_time;
    
    void reset() {
        packets_received = 0;
        packets_lost = 0;
        bytes_received = 0;
        reconnect_count = 0;
        current_fps = 0;
        current_bitrate_kbps = 0;
        average_latency_ms = 0;
        mpp_decode_count = 0;
        rga_process_count = 0;
        software_fallback_count = 0;
        memory_usage_bytes = 0;
        cpu_usage_percent = 0;
        network_errors = 0;
        decode_errors = 0;
        hardware_errors = 0;
        last_packet_time = std::chrono::steady_clock::now();
        connection_start_time = std::chrono::steady_clock::now();
    }
};

// RGA hardware acceleration context
struct RGAContext {
    void* rga_handle = nullptr;
    bool initialized = false;
    HardwareAccelStatus status = HardwareAccelStatus::DISABLED;
    uint32_t operations_count = 0;
    size_t memory_usage_bytes = 0;

    // RGA capabilities
    bool supports_scaling = false;
    bool supports_format_conversion = false;
    bool supports_rotation = false;

    // Performance metrics
    uint32_t average_processing_time_us = 0;
    uint32_t max_processing_time_us = 0;
    uint32_t failed_operations = 0;

    void reset() {
        operations_count = 0;
        memory_usage_bytes = 0;
        average_processing_time_us = 0;
        max_processing_time_us = 0;
        failed_operations = 0;
    }
};

// DMABUF buffer management
struct DMABufContext {
    int dmabuf_fd = -1;
    void* mapped_address = nullptr;
    size_t buffer_size = 0;
    bool is_mapped = false;
    HardwareAccelStatus status = HardwareAccelStatus::DISABLED;

    // Buffer pool management
    std::vector<int> available_buffers;
    std::vector<int> used_buffers;
    size_t total_buffer_count = 0;
    size_t max_buffer_count = 32;  // RK3588 limit

    // Performance metrics
    uint64_t zero_copy_operations = 0;
    uint64_t memory_copy_fallbacks = 0;
    size_t peak_memory_usage = 0;

    void reset() {
        zero_copy_operations = 0;
        memory_copy_fallbacks = 0;
        peak_memory_usage = 0;
    }
};

// Hardware acceleration configuration
struct HardwareAccelConfig {
    // RGA configuration
    bool enable_rga_scaler = true;
    uint32_t rga_max_width = 4096;
    uint32_t rga_max_height = 4096;
    uint32_t rga_timeout_ms = 100;

    // DMABUF configuration
    bool enable_dmabuf_zerocopy = true;
    size_t dmabuf_buffer_size = 2 * 1024 * 1024;  // 2MB per buffer
    uint32_t dmabuf_buffer_count = 16;
    bool dmabuf_cached = false;

    // Performance tuning
    bool enable_hardware_prefetch = true;
    uint32_t hardware_queue_depth = 4;
    bool enable_async_processing = true;

    // Fallback behavior
    bool allow_software_fallback = true;
    uint32_t hardware_error_threshold = 5;  // Switch to software after N errors
    uint32_t hardware_retry_delay_ms = 1000;
};

// System health information for RK3588
struct SystemHealth {
    // Thermal status
    int soc_temperature_celsius;
    bool thermal_throttling_active;

    // Memory status
    size_t total_memory_mb;
    size_t available_memory_mb;
    size_t rtsp_memory_usage_mb;

    // CPU status
    std::vector<float> cpu_usage_per_core;  // 8 cores for RK3588
    float average_cpu_usage;

    // Hardware accelerator status
    HardwareAccelStatus mpp_status;
    HardwareAccelStatus rga_status;
    uint32_t mpp_utilization_percent;
    uint32_t rga_utilization_percent;

    // System load
    uint32_t active_stream_count;
    uint32_t total_stream_count;
    
    SystemHealth() : 
        soc_temperature_celsius(0),
        thermal_throttling_active(false),
        total_memory_mb(0),
        available_memory_mb(0),
        rtsp_memory_usage_mb(0),
        average_cpu_usage(0.0f),
        mpp_status(HardwareAccelStatus::DISABLED),
        rga_status(HardwareAccelStatus::DISABLED),
        mpp_utilization_percent(0),
        rga_utilization_percent(0),
        active_stream_count(0),
        total_stream_count(0) {
        cpu_usage_per_core.resize(8, 0.0f);  // RK3588 has 8 cores
    }
    
    bool isHealthy() const {
        return soc_temperature_celsius < platform::THROTTLE_TEMPERATURE &&
               available_memory_mb > 100 &&  // At least 100MB available
               average_cpu_usage < 90.0f &&
               !thermal_throttling_active;
    }
};

// Result types for error handling
template<typename T>
struct Result {
    T value;
    bool success;
    std::string error_message;
    ErrorCategory error_category;
    
    Result() : success(false), error_category(ErrorCategory::CONFIGURATION_ERROR) {}
    Result(const T& val) : value(val), success(true), error_category(ErrorCategory::CONFIGURATION_ERROR) {}
    Result(const std::string& error, ErrorCategory category) : 
        success(false), error_message(error), error_category(category) {}
    
    operator bool() const { return success; }
    const T& operator*() const { return value; }
    T& operator*() { return value; }
};

// Callback function types
using StreamDataCallback = std::function<void(const StreamId&, const NALUnit&)>;
using StreamEventCallback = std::function<void(const StreamId&, const std::string&)>;
using StreamErrorCallback = std::function<void(const StreamId&, ErrorCategory, const std::string&)>;

} // namespace rtsp
} // namespace aibox
