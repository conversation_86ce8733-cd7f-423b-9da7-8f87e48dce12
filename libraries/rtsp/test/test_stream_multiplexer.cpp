#include <gtest/gtest.h>
#include "rtsp/stream_multiplexer.hpp"
#include "rtsp/rtsp_config.hpp"
#include "rtsp/rtsp_types.hpp"

using namespace aibox::rtsp;

class StreamMultiplexerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration
        config_.max_concurrent_streams = 4;
        config_.max_memory_usage_mb = 100;
        config_.worker_thread_count = 2;
        config_.enable_load_balancing = true;
        config_.enable_thermal_management = true;
        
        // Initialize queue configuration
        config_.queue_config.high_priority_size = 50;
        config_.queue_config.medium_priority_size = 30;
        config_.queue_config.low_priority_size = 20;
        
        multiplexer_ = std::make_unique<StreamMultiplexer>(config_);
    }

    void TearDown() override {
        if (multiplexer_) {
            multiplexer_->stop();
            multiplexer_.reset();
        }
    }

    RTSPConnectionConfig createTestStreamConfig(const std::string& id, StreamPriority priority = StreamPriority::MEDIUM) {
        RTSPConnectionConfig config;
        config.stream_id = id;
        config.rtsp_url = "rtsp://*************:554/stream" + id;
        config.username = "admin";
        config.password = "password";
        config.priority = priority;
        config.enabled = true;
        config.timeout_ms = 5000;
        config.retry_count = 3;
        return config;
    }

    StreamManagementConfig config_;
    std::unique_ptr<StreamMultiplexer> multiplexer_;
};

// Test basic lifecycle
TEST_F(StreamMultiplexerTest, BasicLifecycle) {
    EXPECT_FALSE(multiplexer_->isRunning());
    
    EXPECT_TRUE(multiplexer_->start());
    EXPECT_TRUE(multiplexer_->isRunning());
    
    multiplexer_->stop();
    EXPECT_FALSE(multiplexer_->isRunning());
}

// Test configuration management
TEST_F(StreamMultiplexerTest, ConfigurationManagement) {
    auto initial_config = multiplexer_->getConfig();
    EXPECT_EQ(initial_config.max_concurrent_streams, 4);
    EXPECT_EQ(initial_config.worker_thread_count, 2);
    
    StreamManagementConfig new_config = initial_config;
    new_config.max_concurrent_streams = 8;
    new_config.worker_thread_count = 4;
    
    multiplexer_->updateConfig(new_config);
    
    auto updated_config = multiplexer_->getConfig();
    EXPECT_EQ(updated_config.max_concurrent_streams, 8);
    EXPECT_EQ(updated_config.worker_thread_count, 4);
}

// Test stream management
TEST_F(StreamMultiplexerTest, StreamManagement) {
    auto stream_config = createTestStreamConfig("test1");
    
    // Add stream
    EXPECT_TRUE(multiplexer_->addStream("test1", stream_config));
    
    // Check stream exists
    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 1);
    EXPECT_EQ(stream_ids[0], "test1");
    
    // Get stream info
    auto stream_info = multiplexer_->getStreamInfo("test1");
    EXPECT_EQ(stream_info.id, "test1");
    EXPECT_EQ(stream_info.state, ConnectionState::DISCONNECTED);
    EXPECT_TRUE(stream_info.enabled);
    
    // Remove stream
    EXPECT_TRUE(multiplexer_->removeStream("test1"));
    
    stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 0);
}

// Test multiple streams
TEST_F(StreamMultiplexerTest, MultipleStreams) {
    // Add multiple streams
    for (int i = 1; i <= 3; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }
    
    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 3);
    
    auto stream_infos = multiplexer_->getStreamInfos();
    EXPECT_EQ(stream_infos.size(), 3);
    
    // Check active stream count
    EXPECT_EQ(multiplexer_->getActiveStreamCount(), 0);  // Not connected yet
}

// Test stream limits
TEST_F(StreamMultiplexerTest, StreamLimits) {
    // Try to add more streams than the limit
    for (int i = 1; i <= 5; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        bool result = multiplexer_->addStream(std::to_string(i), config);
        
        if (i <= 4) {
            EXPECT_TRUE(result);  // Should succeed within limit
        } else {
            EXPECT_FALSE(result);  // Should fail beyond limit
        }
    }
    
    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 4);  // Should be limited to max_concurrent_streams
}

// Test stream enable/disable
TEST_F(StreamMultiplexerTest, StreamEnableDisable) {
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));
    
    // Initially enabled
    auto info = multiplexer_->getStreamInfo("test1");
    EXPECT_TRUE(info.enabled);
    
    // Disable stream
    EXPECT_TRUE(multiplexer_->enableStream("test1", false));
    info = multiplexer_->getStreamInfo("test1");
    EXPECT_FALSE(info.enabled);
    
    // Re-enable stream
    EXPECT_TRUE(multiplexer_->enableStream("test1", true));
    info = multiplexer_->getStreamInfo("test1");
    EXPECT_TRUE(info.enabled);
}

// Test priority management
TEST_F(StreamMultiplexerTest, PriorityManagement) {
    auto config = createTestStreamConfig("test1", StreamPriority::MEDIUM);
    EXPECT_TRUE(multiplexer_->addStream("test1", config));
    
    // Check initial priority
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::MEDIUM);
    
    // Change priority
    EXPECT_TRUE(multiplexer_->setStreamPriority("test1", StreamPriority::HIGH));
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::HIGH);
    
    // Change to low priority
    EXPECT_TRUE(multiplexer_->setStreamPriority("test1", StreamPriority::LOW));
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::LOW);
}

// Test statistics collection
TEST_F(StreamMultiplexerTest, StatisticsCollection) {
    auto stats = multiplexer_->getStatistics();
    
    // Initial statistics
    EXPECT_EQ(stats.total_streams, 0);
    EXPECT_EQ(stats.active_streams, 0);
    EXPECT_EQ(stats.connected_streams, 0);
    
    // Add a stream
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));
    
    stats = multiplexer_->getStatistics();
    EXPECT_EQ(stats.total_streams, 1);
    EXPECT_EQ(stats.active_streams, 1);  // Enabled by default
    EXPECT_EQ(stats.connected_streams, 0);  // Not connected yet
}

// Test system health monitoring
TEST_F(StreamMultiplexerTest, SystemHealthMonitoring) {
    auto health = multiplexer_->getSystemHealth();
    
    // Initial health should be good
    EXPECT_FALSE(health.status.empty());
    EXPECT_GE(health.score, 0.0f);
    EXPECT_LE(health.score, 1.0f);
    EXPECT_EQ(health.active_streams, 0);
    EXPECT_EQ(health.error_count, 0);
}

// Test load balancing
TEST_F(StreamMultiplexerTest, LoadBalancing) {
    EXPECT_TRUE(multiplexer_->isLoadBalancingEnabled());
    
    multiplexer_->enableLoadBalancing(false);
    EXPECT_FALSE(multiplexer_->isLoadBalancingEnabled());
    
    multiplexer_->enableLoadBalancing(true);
    EXPECT_TRUE(multiplexer_->isLoadBalancingEnabled());
    
    // Test rebalancing (should not crash)
    multiplexer_->rebalanceStreams();
    multiplexer_->redistributeLoad();
}

// Test memory usage tracking
TEST_F(StreamMultiplexerTest, MemoryUsageTracking) {
    size_t initial_memory = multiplexer_->getTotalMemoryUsage();
    
    // Add streams and check memory increases
    for (int i = 1; i <= 3; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }
    
    size_t memory_with_streams = multiplexer_->getTotalMemoryUsage();
    EXPECT_GT(memory_with_streams, initial_memory);
}

// Test error handling
TEST_F(StreamMultiplexerTest, ErrorHandling) {
    // Try to remove non-existent stream
    EXPECT_FALSE(multiplexer_->removeStream("nonexistent"));
    
    // Try to enable non-existent stream
    EXPECT_FALSE(multiplexer_->enableStream("nonexistent", true));
    
    // Try to set priority for non-existent stream
    EXPECT_FALSE(multiplexer_->setStreamPriority("nonexistent", StreamPriority::HIGH));
    
    // Try to get info for non-existent stream
    auto info = multiplexer_->getStreamInfo("nonexistent");
    EXPECT_TRUE(info.id.empty());  // Should return default-constructed info
}

// Test callbacks
TEST_F(StreamMultiplexerTest, CallbackFunctionality) {
    bool nal_callback_called = false;
    bool event_callback_called = false;
    bool error_callback_called = false;
    
    multiplexer_->setNALUnitCallback([&](const StreamId& id, const NALUnit& nal) {
        nal_callback_called = true;
    });
    
    multiplexer_->setStreamEventCallback([&](const StreamId& id, const std::string& event) {
        event_callback_called = true;
    });
    
    multiplexer_->setErrorCallback([&](const StreamId& id, ErrorCategory category, const std::string& message) {
        error_callback_called = true;
    });
    
    // Callbacks are set (actual invocation would require real stream data)
    EXPECT_TRUE(true);  // Test passes if no exceptions thrown
}

// Test thermal management
TEST_F(StreamMultiplexerTest, ThermalManagement) {
    // Add some streams
    for (int i = 1; i <= 2; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }
    
    // Test thermal throttling (should not crash)
    multiplexer_->handleThermalThrottling(75);  // Normal temperature
    multiplexer_->handleThermalThrottling(85);  // High temperature
    
    // Test performance mode changes
    multiplexer_->setPerformanceMode(true);
    multiplexer_->setPerformanceMode(false);
}

// Test resource management
TEST_F(StreamMultiplexerTest, ResourceManagement) {
    auto config = createTestStreamConfig("test1");
    
    // Check resource availability
    EXPECT_TRUE(multiplexer_->checkResourceAvailability(config));
    
    // Test resource optimization (should not crash)
    multiplexer_->optimizeResourceUsage();
    multiplexer_->handleMemoryPressure();
}
